from fastapi import <PERSON><PERSON><PERSON>, HTT<PERSON>Exception, Depends, Header, Request, status, Body
from fastapi.responses import RedirectResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import spotipy
from spotipy.oauth2 import SpotifyOAuth
import os
from dotenv import load_dotenv
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime
import random

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configuration variables
SPOTIFY_CLIENT_ID = os.getenv('SPOTIFY_CLIENT_ID')
SPOTIFY_CLIENT_SECRET = os.getenv('SPOTIFY_CLIENT_SECRET')
SPOTIFY_REDIRECT_URI = os.getenv('SPOTIFY_REDIRECT_URI')
FRONTEND_URL = os.getenv('FRONTEND_URL')
BACKEND_URL = os.getenv('BACKEND_URL')
SPOTIFY_SCOPES = os.getenv('SPOTIFY_SCOPES').split()

# Request/Response Models
class AuthUrlResponse(BaseModel):
    auth_url: str = Field(..., description="The Spotify authorization URL to redirect the user to")
    
    class Config:
        json_schema_extra = {
            "example": {
                "auth_url": "https://accounts.spotify.com/authorize?client_id=xxx&response_type=code..."
            }
        }

class TokenRequest(BaseModel):
    code: str = Field(
        ..., 
        description="The authorization code received from Spotify after user approval. "
                   "This is the 'code' parameter from the callback URL, NOT the entire URL."
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "code": "AQD5JGwM..."  # Just the code, not the full URL
            }
        }

class TokenResponse(BaseModel):
    access_token: str = Field(..., description="The Spotify access token")
    refresh_token: str = Field(..., description="The Spotify refresh token")

    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "BQDKxXCq...",
                "refresh_token": "AQAnpK8K..."
            }
        }

# Playlist Models
class SpotifyImage(BaseModel):
    url: str
    height: Optional[int] = None
    width: Optional[int] = None

class SpotifyUser(BaseModel):
    id: str
    display_name: str
    type: str

class SpotifyArtist(BaseModel):
    id: str
    name: str
    uri: str

class SpotifyAlbum(BaseModel):
    id: str
    name: str
    images: List[SpotifyImage] = []

class SpotifyTrack(BaseModel):
    id: str
    name: str
    uri: str
    duration_ms: int
    artists: List[SpotifyArtist]
    album: SpotifyAlbum

class Playlist(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    images: List[SpotifyImage] = []
    owner: SpotifyUser
    public: bool
    collaborative: bool
    tracks: Optional[List[SpotifyTrack]] = None
    uri: str
    snapshot_id: str

class PlaylistsResponse(BaseModel):
    items: List[Playlist]
    total: int

app = FastAPI(
    title="Spotify App Backend",
    description="""
    FastAPI backend for Spotify integration.
    
    Authentication Flow:
    1. Call GET /auth/login to get the Spotify authorization URL
    2. Redirect user to that URL
    3. After user approves, Spotify redirects to your frontend with a code
    4. Send that code to POST /auth/token to get access and refresh tokens
    """,
    version="1.0.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[FRONTEND_URL],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint returning API information."""
    return {
        "name": "Spotify App API",
        "version": "1.0.0",
        "status": "operational",
        "docs": "/docs",
        "healthcheck": "/health"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "environment": "development" if FRONTEND_URL.startswith("http://localhost") else "production",
        "spotify_integration": bool(SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET)
    }

def get_spotify_oauth():
    """Create a new SpotifyOAuth instance."""
    if not all([SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET, SPOTIFY_REDIRECT_URI]):
        logger.error("Missing Spotify configuration")
        logger.error(f"REDIRECT_URI: {SPOTIFY_REDIRECT_URI}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Spotify configuration is incomplete"
        )

    return SpotifyOAuth(
        client_id=SPOTIFY_CLIENT_ID,
        client_secret=SPOTIFY_CLIENT_SECRET,
        redirect_uri=SPOTIFY_REDIRECT_URI,
        scope=' '.join(SPOTIFY_SCOPES),
        open_browser=False,
        show_dialog=True
    )

def get_spotify_client(token: str):
    """Create a Spotify client with the given access token."""
    return spotipy.Spotify(auth=token)

def get_token_from_header(authorization: str = Header(...)):
    """Extract token from Authorization header."""
    if not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format"
        )
    return authorization.split(" ")[1]

def fisher_yates_shuffle(tracks: List[Any]) -> List[Any]:
    """
    Implement Fisher-Yates shuffle algorithm for true randomization.
    This provides better randomization than Python's built-in random.shuffle.
    """
    shuffled = tracks.copy()
    for i in range(len(shuffled) - 1, 0, -1):
        j = random.randint(0, i)
        shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
    return shuffled

@app.get("/auth/login", response_model=AuthUrlResponse)
async def login():
    """
    Start the Spotify OAuth flow.
    
    Returns a Spotify authorization URL. Redirect the user to this URL.
    After approval, Spotify will redirect to your frontend with a code parameter.
    Use that code with the /auth/token endpoint.
    """
    try:
        spotify_oauth = get_spotify_oauth()
        auth_url = spotify_oauth.get_authorize_url()
        logger.info(f"Generated auth URL: {auth_url}")
        return {"auth_url": auth_url}
    except Exception as e:
        logger.error(f"Login failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initialize login"
        )

@app.post("/auth/token", response_model=TokenResponse)
async def exchange_token(request: TokenRequest):
    """
    Exchange authorization code for tokens.
    
    After the user approves your app on Spotify, they are redirected to your frontend
    with a code parameter. Send ONLY that code (not the full URL) in the request body.
    
    Example callback URL from Spotify:
    http://localhost:5173/callback?code=AQD5JGwM...
    
    Example request body:
    {
        "code": "AQD5JGwM..."
    }
    """
    try:
        logger.info("Exchanging code for tokens")
        spotify_oauth = get_spotify_oauth()
        token_info = spotify_oauth.get_access_token(request.code, as_dict=True, check_cache=False)
        
        if not token_info or 'access_token' not in token_info:
            logger.error("No token info received")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid authorization code"
            )
        
        logger.info("Successfully exchanged code for tokens")
        return {
            "access_token": token_info['access_token'],
            "refresh_token": token_info['refresh_token']
        }
    except Exception as e:
        logger.error(f"Error exchanging token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@app.get("/auth/refresh")
async def refresh_token(refresh_token: str):
    """Refresh the access token."""
    try:
        spotify_oauth = get_spotify_oauth()
        new_token = spotify_oauth.refresh_access_token(refresh_token)
        return {"access_token": new_token['access_token'], "refresh_token": refresh_token}
    except Exception as e:
        logger.error(f"Error refreshing token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token refresh failed"
        )

@app.get("/api/me")
async def get_current_user(token: str = Depends(get_token_from_header)):
    """Get current user profile."""
    try:
        sp = get_spotify_client(token)
        user = sp.current_user()
        return user
    except Exception as e:
        logger.error(f"Error fetching user profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Failed to fetch user profile"
        )

@app.get("/api/playlists", response_model=PlaylistsResponse)
async def get_user_playlists(
    limit: int = 50,
    offset: int = 0,
    token: str = Depends(get_token_from_header)
):
    """Get user's playlists."""
    try:
        sp = get_spotify_client(token)
        results = sp.current_user_playlists(limit=limit, offset=offset)

        playlists = []
        for item in results['items']:
            playlist = Playlist(
                id=item['id'],
                name=item['name'],
                description=item.get('description'),
                images=[SpotifyImage(**img) for img in item.get('images', [])],
                owner=SpotifyUser(
                    id=item['owner']['id'],
                    display_name=item['owner']['display_name'],
                    type=item['owner']['type']
                ),
                public=item['public'],
                collaborative=item['collaborative'],
                uri=item['uri'],
                snapshot_id=item['snapshot_id']
            )
            playlists.append(playlist)

        return PlaylistsResponse(
            items=playlists,
            total=results['total']
        )
    except Exception as e:
        logger.error(f"Error fetching playlists: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch playlists"
        )

@app.get("/api/liked-songs")
async def get_liked_songs(
    limit: int = 50,
    offset: int = 0,
    token: str = Depends(get_token_from_header)
):
    """Get user's liked songs (saved tracks)."""
    try:
        sp = get_spotify_client(token)
        results = sp.current_user_saved_tracks(limit=limit, offset=offset)

        tracks = []
        for item in results['items']:
            if item['track'] and item['track']['id']:
                track_data = item['track']
                track = SpotifyTrack(
                    id=track_data['id'],
                    name=track_data['name'],
                    uri=track_data['uri'],
                    duration_ms=track_data['duration_ms'],
                    artists=[
                        SpotifyArtist(
                            id=artist['id'],
                            name=artist['name'],
                            uri=artist['uri']
                        ) for artist in track_data['artists']
                    ],
                    album=SpotifyAlbum(
                        id=track_data['album']['id'],
                        name=track_data['album']['name'],
                        images=[SpotifyImage(**img) for img in track_data['album'].get('images', [])]
                    )
                )
                tracks.append(track)

        return {
            "items": tracks,
            "total": results['total'],
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        logger.error(f"Error fetching liked songs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch liked songs"
        )

@app.post("/api/playlists/{playlist_id}/shuffle")
async def shuffle_playlist(
    playlist_id: str,
    token: str = Depends(get_token_from_header)
):
    """Shuffle a playlist using Fisher-Yates algorithm for true randomization."""
    try:
        sp = get_spotify_client(token)

        # Get playlist details
        playlist = sp.playlist(playlist_id)

        # Get all tracks from the playlist
        tracks = []
        results = sp.playlist_tracks(playlist_id)
        tracks.extend(results['items'])

        # Handle pagination
        while results['next']:
            results = sp.next(results)
            tracks.extend(results['items'])

        # Filter out None tracks and extract track data
        valid_tracks = []
        for item in tracks:
            if item['track'] and item['track']['id']:
                track_data = item['track']
                track = SpotifyTrack(
                    id=track_data['id'],
                    name=track_data['name'],
                    uri=track_data['uri'],
                    duration_ms=track_data['duration_ms'],
                    artists=[
                        SpotifyArtist(
                            id=artist['id'],
                            name=artist['name'],
                            uri=artist['uri']
                        ) for artist in track_data['artists']
                    ],
                    album=SpotifyAlbum(
                        id=track_data['album']['id'],
                        name=track_data['album']['name'],
                        images=[SpotifyImage(**img) for img in track_data['album'].get('images', [])]
                    )
                )
                valid_tracks.append(track)

        # Apply Fisher-Yates shuffle
        shuffled_tracks = fisher_yates_shuffle(valid_tracks)

        # Create response playlist
        shuffled_playlist = Playlist(
            id=playlist['id'],
            name=playlist['name'],
            description=playlist.get('description'),
            images=[SpotifyImage(**img) for img in playlist.get('images', [])],
            owner=SpotifyUser(
                id=playlist['owner']['id'],
                display_name=playlist['owner']['display_name'],
                type=playlist['owner']['type']
            ),
            public=playlist['public'],
            collaborative=playlist['collaborative'],
            tracks=shuffled_tracks,
            uri=playlist['uri'],
            snapshot_id=playlist['snapshot_id']
        )

        return shuffled_playlist

    except Exception as e:
        logger.error(f"Error shuffling playlist: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to shuffle playlist: {str(e)}"
        )

@app.post("/api/liked-songs/shuffle")
async def shuffle_liked_songs(token: str = Depends(get_token_from_header)):
    """Shuffle user's liked songs using Fisher-Yates algorithm."""
    try:
        sp = get_spotify_client(token)

        # Get all liked songs
        tracks = []
        results = sp.current_user_saved_tracks(limit=50)
        tracks.extend(results['items'])

        # Handle pagination
        while results['next']:
            results = sp.next(results)
            tracks.extend(results['items'])

        # Filter out None tracks and extract track data
        valid_tracks = []
        for item in tracks:
            if item['track'] and item['track']['id']:
                track_data = item['track']
                track = SpotifyTrack(
                    id=track_data['id'],
                    name=track_data['name'],
                    uri=track_data['uri'],
                    duration_ms=track_data['duration_ms'],
                    artists=[
                        SpotifyArtist(
                            id=artist['id'],
                            name=artist['name'],
                            uri=artist['uri']
                        ) for artist in track_data['artists']
                    ],
                    album=SpotifyAlbum(
                        id=track_data['album']['id'],
                        name=track_data['album']['name'],
                        images=[SpotifyImage(**img) for img in track_data['album'].get('images', [])]
                    )
                )
                valid_tracks.append(track)

        if not valid_tracks:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No liked songs found"
            )

        # Shuffle using Fisher-Yates algorithm
        shuffled_tracks = fisher_yates_shuffle(valid_tracks)

        # Create a virtual "Liked Songs" playlist response
        shuffled_playlist = {
            "id": "liked-songs",
            "name": "Liked Songs",
            "description": "Your liked songs, shuffled",
            "images": [],
            "owner": {"id": "spotify", "display_name": "Spotify", "type": "user"},
            "public": False,
            "collaborative": False,
            "tracks": shuffled_tracks,
            "uri": "spotify:collection:tracks",
            "snapshot_id": "liked-songs-shuffled"
        }

        logger.info(f"Successfully shuffled {len(shuffled_tracks)} liked songs")
        return shuffled_playlist

    except Exception as e:
        logger.error(f"Error shuffling liked songs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to shuffle liked songs"
        )
