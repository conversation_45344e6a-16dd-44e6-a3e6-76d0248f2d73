# Environment variables
.env
.env.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.venv
venv/

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Frontend build
dist/
.vite/
.cache/

# IDEs
.idea/
.vscode/
*.swp
*.swo

# Docker
.docker/
docker-compose.override.yml

# Logs
logs/
*.log

# System
.DS_Store
Thumbs.db 