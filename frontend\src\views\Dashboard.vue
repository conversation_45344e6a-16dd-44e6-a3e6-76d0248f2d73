<template>
  <div class="w-full h-full bg-background">
    <!-- Main Grid Layout -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-min">
      <!-- Welcome Card -->
      <div class="col-span-full p-4 sm:p-6 bg-card rounded-lg border">
        <div class="flex flex-col space-y-4">
          <div class="flex items-center space-x-2">
            <h3 class="text-base sm:text-lg font-semibold">
              🎵 Welcome to Moko
            </h3>
          </div>
          <p class="text-sm text-muted-foreground">
            Select a playlist from the sidebar to start true randomization!
          </p>
        </div>
      </div>

      <!-- Shuffle Status Card -->
      <div v-if="isShuffling" class="col-span-full p-4 sm:p-6 bg-card rounded-lg border">
        <div class="flex flex-col space-y-4">
          <div class="flex items-center space-x-2">
            <h3 class="text-base sm:text-lg font-semibold">
              {{ trackCount > 500 
                ? "🚨 Large Playlist Shuffle" 
                : trackCount > 50 
                  ? "🎵 Shuffling Collection" 
                  : "🎲 Quick Shuffle" }}
            </h3>
            <div class="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
          <p class="text-sm whitespace-pre-line text-muted-foreground">
            {{ getProgressMessage(trackCount, shuffleProgress) }}
          </p>
          <div v-if="trackCount > maxSafePlaylistSize" class="text-xs text-yellow-600 dark:text-yellow-400">
            ⚠️ Large playlists might take longer or encounter issues
          </div>
        </div>
      </div>

      <!-- Error Card -->
      <div v-if="shuffleError" class="col-span-full p-4 border-red-500 bg-red-50 dark:bg-red-900/10 rounded-lg border">
        <div class="flex flex-col space-y-2">
          <p class="text-red-600 dark:text-red-400">{{ shuffleError }}</p>
          <p class="text-sm text-red-500 dark:text-red-300">
            Try again with a smaller playlist or contact support if the issue persists.
          </p>
        </div>
      </div>

      <!-- Playlist Display -->
      <div v-if="randomizedPlaylist" class="col-span-full">
        <div class="p-4 bg-card rounded-lg border">
          <div class="flex flex-col space-y-4">
            <!-- Playlist Header -->
            <div class="flex items-center space-x-4">
              <img 
                v-if="randomizedPlaylist.images?.[0]?.url"
                :src="randomizedPlaylist.images[0].url"
                :alt="randomizedPlaylist.name"
                class="w-16 h-16 rounded-lg"
              />
              <div v-else class="w-16 h-16 bg-accent rounded-lg flex items-center justify-center">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                </svg>
              </div>
              <div>
                <h2 class="text-lg font-semibold">{{ randomizedPlaylist.name }}</h2>
                <p class="text-sm text-muted-foreground">
                  {{ randomizedPlaylist.tracks?.length || 0 }} tracks
                </p>
              </div>
            </div>

            <!-- Tracks Grid -->
            <div class="grid gap-2">
              <div 
                v-for="(track, index) in paginatedTracks"
                :key="track.id"
                class="flex items-center gap-3 p-2 rounded-lg hover:bg-accent/50 transition-colors"
              >
                <span class="w-8 text-center text-muted-foreground">
                  {{ (currentPage - 1) * tracksPerPage + index + 1 }}
                </span>
                <img 
                  v-if="track.album.images?.[0]"
                  :src="track.album.images[0].url"
                  :alt="track.album.name"
                  class="w-10 h-10 rounded"
                />
                <div v-else class="w-10 h-10 bg-accent rounded flex items-center justify-center">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium truncate">{{ track.name }}</p>
                  <p class="text-xs text-muted-foreground truncate">
                    {{ track.artists.map(a => a.name).join(', ') }}
                  </p>
                </div>
                <span class="text-xs text-muted-foreground">
                  {{ formatDuration(track.duration_ms) }}
                </span>
              </div>
            </div>

            <!-- Pagination -->
            <div v-if="totalPages > 1" class="flex justify-center items-center space-x-2 mt-4">
              <button
                @click="currentPage = Math.max(1, currentPage - 1)"
                :disabled="currentPage === 1"
                class="px-3 py-1 rounded-md bg-accent hover:bg-accent/80 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span class="text-sm text-muted-foreground">
                Page {{ currentPage }} of {{ totalPages }}
              </span>
              <button
                @click="currentPage = Math.min(totalPages, currentPage + 1)"
                :disabled="currentPage === totalPages"
                class="px-3 py-1 rounded-md bg-accent hover:bg-accent/80 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

const maxSafePlaylistSize = 100
const tracksPerPage = 20

interface SpotifyImage {
  url: string
  height: number | null
  width: number | null
}

interface SpotifyArtist {
  id: string
  name: string
  uri: string
}

interface SpotifyAlbum {
  id: string
  name: string
  images: SpotifyImage[]
}

interface SpotifyTrack {
  id: string
  name: string
  uri: string
  duration_ms: number
  artists: SpotifyArtist[]
  album: SpotifyAlbum
}

interface RandomizedPlaylist {
  id: string
  name: string
  description?: string
  images?: SpotifyImage[]
  tracks: SpotifyTrack[]
  uri: string
  snapshot_id: string
}

// State
const randomizedPlaylist = ref<RandomizedPlaylist | null>(null)
const currentPage = ref(1)
const isShuffling = ref(false)
const shuffleProgress = ref(0)
const shuffleError = ref<string | null>(null)
const trackCount = ref(0)

// Computed
const paginatedTracks = computed(() => {
  if (!randomizedPlaylist.value?.tracks) return []
  return randomizedPlaylist.value.tracks.slice(
    (currentPage.value - 1) * tracksPerPage,
    currentPage.value * tracksPerPage
  )
})

const totalPages = computed(() => {
  if (!randomizedPlaylist.value?.tracks) return 0
  return Math.ceil(randomizedPlaylist.value.tracks.length / tracksPerPage)
})

// Methods
const getProgressMessage = (trackCount: number, progress: number) => {
  const percentage = Math.round(progress)
  const processedTracks = Math.round((trackCount * progress) / 100)
  const baseMessage = `Processing ${percentage}% (${processedTracks} of ${trackCount} tracks)`
  
  if (trackCount <= 50) {
    return `${baseMessage} 🎲`
  }

  let stageMessage = ""
  if (progress < 33) {
    stageMessage = trackCount > 500 
      ? "\n⏳ Large playlist - this might take a while..."
      : "\n🚀 Shuffle engine warming up..."
  } else if (progress < 66) {
    stageMessage = trackCount > 500
      ? "\n📱 Still working on it..."
      : "\n🎵 Making progress!"
  } else if (progress < 95) {
    stageMessage = trackCount > 500
      ? "\n🌟 Final stretch!"
      : "\n🎸 Almost there!"
  }
  
  return `${baseMessage}${stageMessage}`
}

const formatDuration = (ms: number) => {
  const minutes = Math.floor(ms / 60000)
  const seconds = Math.floor((ms % 60000) / 1000)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

const handleShuffleEvent = (event: MessageEvent) => {
  if (!event.data.type || !event.data.type.startsWith('SHUFFLE_')) return
  
  console.log('Processing shuffle event:', event.data)

  if (event.data.type === 'SHUFFLE_START') {
    const eventTrackCount = event.data.trackCount || 0
    
    isShuffling.value = true
    shuffleError.value = null
    shuffleProgress.value = 0
    trackCount.value = eventTrackCount

  } else if (event.data.type === 'SHUFFLE_PROGRESS') {
    isShuffling.value = true
    shuffleError.value = null
    shuffleProgress.value = event.data.progress
    
    if (event.data.playlist) {
      randomizedPlaylist.value = {
        ...randomizedPlaylist.value,
        ...event.data.playlist,
        tracks: randomizedPlaylist.value?.tracks || []
      }
    }
  } else if (event.data.type === 'SHUFFLE_COMPLETE') {
    isShuffling.value = false
    shuffleError.value = null
    randomizedPlaylist.value = {
      ...event.data.playlist,
      tracks: event.data.playlist.tracks || []
    }
  } else if (event.data.type === 'SHUFFLE_ERROR') {
    isShuffling.value = false
    const errorMessage = event.data.error || "An unknown error occurred"
    shuffleError.value = errorMessage
    randomizedPlaylist.value = null
  }
}

// Lifecycle
onMounted(() => {
  window.addEventListener('message', handleShuffleEvent)
})

onUnmounted(() => {
  window.removeEventListener('message', handleShuffleEvent)
})
</script>
