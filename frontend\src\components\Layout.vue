<template>
  <div class="h-screen bg-background">
    <Header />
    <Sidebar />
    <main class="pt-16 pl-20 md:pl-56 h-screen overflow-auto bg-background p-4 sm:p-6">
      <RouterView />
    </main>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Watch for authentication changes
watch(() => authStore.isAuthenticated, (isAuth) => {
  if (!isAuth && !authStore.isLoading) {
    const returnTo = encodeURIComponent(route.path + (route.query ? '?' + new URLSearchParams(route.query as Record<string, string>).toString() : ''))
    router.push(`/?returnTo=${returnTo}`)
  }
})

// Check authentication on mount
onMounted(() => {
  if (!authStore.isAuthenticated && !authStore.isLoading) {
    const returnTo = encodeURIComponent(route.path + (route.query ? '?' + new URLSearchParams(route.query as Record<string, string>).toString() : ''))
    router.push(`/?returnTo=${returnTo}`)
  }
})
</script>
