# Moko - Spotify Randomizer Frontend

A Vue 3 + TypeScript + Vite frontend application for the Moko Spotify Randomizer. This application provides true randomization for Spotify playlists using the Fisher<PERSON><PERSON> shuffle algorithm.

## Features

- **True Randomization**: Uses Fisher<PERSON><PERSON> shuffle algorithm instead of Spotify's algorithmic shuffle
- **Vue 3 + Composition API**: Modern Vue.js with TypeScript support
- **Tailwind CSS**: Utility-first CSS framework for responsive design
- **Spotify OAuth**: Secure authentication with Spotify Web API
- **Pinia State Management**: Reactive state management for authentication and playlists
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Tech Stack

- **Vue 3** with Composition API and `<script setup>` syntax
- **TypeScript** for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Pinia** for state management
- **Vue Router** for navigation
- **Axios** for HTTP requests

## Development Setup

1. Install dependencies:
```bash
npm install
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Update the `.env` file with your configuration:
```env
VITE_BACKEND_URL=http://localhost:8080
VITE_APP_NAME=Moko - Spotify Randomizer
VITE_APP_VERSION=2.0.0
```

4. Start the development server:
```bash
npm run dev
```

## Build for Production

```bash
npm run build
```

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Vue - Official](https://marketplace.visualstudio.com/items?itemName=Vue.volar) extension

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```
