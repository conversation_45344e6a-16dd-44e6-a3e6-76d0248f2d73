<template>
  <header class="fixed top-0 left-0 right-0 bg-card border-b border-border px-4 py-3 flex items-center justify-between z-20 h-16">
    <div class="flex items-center space-x-4">
      <h1 class="text-xl font-bold text-foreground"><PERSON><PERSON></h1>
    </div>

    <div class="flex items-center space-x-4">
      <!-- Theme Toggle -->
      <button
        @click="toggleTheme"
        class="p-2 rounded-md hover:bg-accent transition-colors"
        :title="isDark ? 'Switch to light mode' : 'Switch to dark mode'"
      >
        <Sun v-if="isDark" class="h-5 w-5" />
        <Moon v-else class="h-5 w-5" />
      </button>

      <!-- User Profile -->
      <div v-if="authStore.profile" class="flex items-center space-x-2">
        <img
          v-if="authStore.profile.images?.[0]?.url"
          :src="authStore.profile.images[0].url"
          :alt="authStore.profile.display_name"
          class="w-8 h-8 rounded-full"
        />
        <div v-else class="w-8 h-8 rounded-full bg-accent flex items-center justify-center">
          <span class="text-sm font-medium">
            {{ authStore.profile.display_name?.charAt(0).toUpperCase() }}
          </span>
        </div>
        <span class="text-sm font-medium hidden sm:inline">
          {{ authStore.profile.display_name }}
        </span>
      </div>

      <!-- Logout Button -->
      <button
        @click="handleLogout"
        class="px-3 py-1 text-sm bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 transition-colors"
      >
        Logout
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Sun, Moon } from 'lucide-vue-next'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const isDark = ref(false)

// Theme management
const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

// Initialize theme
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  
  isDark.value = savedTheme === 'dark' || (!savedTheme && prefersDark)
  document.documentElement.classList.toggle('dark', isDark.value)
})

const handleLogout = () => {
  authStore.logout()
  router.push('/')
}
</script>
