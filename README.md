# Moko - Spotify Randomizer

A modern web application that provides **true randomization** for Spotify playlists using the <PERSON><PERSON><PERSON> shuffle algorithm, replacing Spotify's algorithm-influenced shuffle with genuine randomness.

## 🎵 Features

- **True Random Shuffle**: Uses <PERSON><PERSON><PERSON> algorithm for genuine randomization
- **Spotify Integration**: Full OAuth authentication with Spotify Web API
- **Modern UI**: Responsive Vue 3 + Tailwind CSS interface
- **Fast Performance**: Vite-powered frontend with FastAPI backend
- **Docker Support**: Containerized deployment for easy setup
- **TypeScript**: Full type safety across the application

## 🏗️ Architecture

- **Frontend**: Vue 3 + TypeScript + Vite + Tailwind CSS + Pinia
- **Backend**: FastAPI + Python + Pydantic
- **Authentication**: Spotify OAuth 2.0
- **Deployment**: Docker + Docker Compose

## 🚀 Quick Start

1. **Clone the repository**:
```bash
git clone https://github.com/calmren/moko.git
cd moko
```

2. **Set up environment variables**:
```bash
cp .env.example .env
```

3. **Configure Spotify credentials** in `.env`:
```env
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
```

4. **Start with Docker**:
```bash
docker-compose up --build
```

5. **Access the application**:
   - Frontend: http://localhost:5176
   - Backend API: http://localhost:8080

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

### Backend Development
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8080
```

## 📁 Project Structure

```
moko/
├── frontend/           # Vue 3 + TypeScript frontend
│   ├── src/
│   │   ├── components/ # Reusable Vue components
│   │   ├── views/      # Page components
│   │   ├── stores/     # Pinia state management
│   │   └── router/     # Vue Router configuration
│   └── package.json
├── backend/            # FastAPI Python backend
│   ├── main.py         # FastAPI application
│   ├── requirements.txt
│   └── Dockerfile
├── docker-compose.yml  # Docker orchestration
└── .env.example        # Environment variables template
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SPOTIFY_CLIENT_ID` | Spotify App Client ID | Required |
| `SPOTIFY_CLIENT_SECRET` | Spotify App Client Secret | Required |
| `FRONTEND_URL` | Frontend application URL | `http://localhost:5176` |
| `BACKEND_URL` | Backend API URL | `http://localhost:8080` |
| `SPOTIFY_REDIRECT_URI` | OAuth callback URL | `http://localhost:5176/callback` |

### Spotify App Setup

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app
3. Add redirect URI: `http://localhost:5176/callback`
4. Copy Client ID and Client Secret to your `.env` file

## 🎯 How It Works

1. **Authentication**: Users log in with their Spotify account
2. **Playlist Access**: App fetches user's playlists via Spotify Web API
3. **True Randomization**: Fisher-Yates shuffle algorithm randomizes track order
4. **Playlist Update**: Shuffled playlist is saved back to Spotify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Spotify Web API for music data access
- Vue.js and FastAPI communities for excellent frameworks
- Fisher-Yates shuffle algorithm for true randomization
