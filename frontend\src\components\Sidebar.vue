<template>
  <aside class="fixed left-0 top-0 h-full w-20 md:w-56 bg-card border-r border-gray-200 dark:border-gray-700 pt-16 z-10">
    <div class="flex flex-col h-full">
      <!-- Navigation Links -->
      <nav class="flex-1 px-2 py-4 space-y-2">
        <div class="flex items-center px-3 py-2 text-sm font-medium text-muted-foreground">
          <Home class="h-5 w-5 md:mr-3" />
          <span class="hidden md:inline">Home</span>
        </div>
        
        <div class="flex items-center px-3 py-2 text-sm font-medium text-muted-foreground">
          <Search class="h-5 w-5 md:mr-3" />
          <span class="hidden md:inline">Search</span>
        </div>
        
        <div class="flex items-center px-3 py-2 text-sm font-medium text-muted-foreground">
          <Heart class="h-5 w-5 md:mr-3" />
          <span class="hidden md:inline">Liked Songs</span>
        </div>
      </nav>

      <!-- Playlists Section -->
      <div class="flex-1 px-2 py-4">
        <div class="flex items-center justify-between px-3 py-2 text-sm font-medium text-muted-foreground">
          <span class="hidden md:inline">Playlists</span>
          <button
            @click="fetchPlaylists"
            :disabled="isLoading"
            class="p-1 rounded hover:bg-accent transition-colors"
            title="Refresh playlists"
          >
            <RotateCcw :class="['h-4 w-4', { 'animate-spin': isLoading }]" />
          </button>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="px-3 py-2 text-sm text-muted-foreground">
          <div class="flex items-center">
            <div class="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full md:mr-3"></div>
            <span class="hidden md:inline">Loading playlists...</span>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="px-3 py-2 text-sm text-red-500">
          <div class="flex items-center">
            <AlertCircle class="h-4 w-4 md:mr-3" />
            <span class="hidden md:inline">Failed to load</span>
          </div>
        </div>

        <!-- Playlists List -->
        <div v-else class="space-y-1 max-h-96 overflow-y-auto">
          <button
            v-for="playlist in playlists"
            :key="playlist.id"
            @click="selectPlaylist(playlist)"
            class="w-full flex items-center px-3 py-2 text-sm text-left hover:bg-accent rounded-md transition-colors group"
            :class="{ 'bg-accent': selectedPlaylistId === playlist.id }"
          >
            <img
              v-if="playlist.images?.[0]?.url"
              :src="playlist.images[0].url"
              :alt="playlist.name"
              class="w-8 h-8 rounded md:mr-3 flex-shrink-0"
            />
            <div v-else class="w-8 h-8 bg-accent rounded flex items-center justify-center md:mr-3 flex-shrink-0">
              <Music class="h-4 w-4" />
            </div>
            <div class="hidden md:block min-w-0 flex-1">
              <p class="font-medium truncate">{{ playlist.name }}</p>
              <p class="text-xs text-muted-foreground truncate">
                {{ playlist.owner?.display_name }}
              </p>
            </div>
          </button>
        </div>
      </div>

      <!-- Shuffle Button -->
      <div v-if="selectedPlaylist" class="p-4 border-t border-gray-200 dark:border-gray-700">
        <button
          @click="shufflePlaylist"
          :disabled="isShuffling"
          class="w-full bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center"
        >
          <Shuffle class="h-4 w-4 md:mr-2" />
          <span class="hidden md:inline">
            {{ isShuffling ? 'Shuffling...' : 'Shuffle' }}
          </span>
        </button>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Home, Search, Heart, Music, RotateCcw, AlertCircle, Shuffle } from 'lucide-vue-next'
import { useAuthStore } from '../stores/auth'

interface SpotifyImage {
  url: string
  height: number | null
  width: number | null
}

interface Playlist {
  id: string
  name: string
  images?: SpotifyImage[]
  owner?: {
    display_name: string
  }
}

const authStore = useAuthStore()
const playlists = ref<Playlist[]>([])
const selectedPlaylist = ref<Playlist | null>(null)
const selectedPlaylistId = ref<string | null>(null)
const isLoading = ref(false)
const isShuffling = ref(false)
const error = ref<string | null>(null)

const fetchPlaylists = async () => {
  if (!authStore.token) return

  try {
    isLoading.value = true
    error.value = null

    const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'}/api/playlists`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch playlists')
    }

    const data = await response.json()
    playlists.value = data.items || []
  } catch (err) {
    console.error('Error fetching playlists:', err)
    error.value = 'Failed to load playlists'
  } finally {
    isLoading.value = false
  }
}

const selectPlaylist = (playlist: Playlist) => {
  selectedPlaylist.value = playlist
  selectedPlaylistId.value = playlist.id
}

const shufflePlaylist = async () => {
  if (!selectedPlaylist.value || !authStore.token) return

  try {
    isShuffling.value = true
    
    // Emit shuffle start event
    window.postMessage({
      type: 'SHUFFLE_START',
      trackCount: 0 // We'll get this from the API
    }, '*')

    const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'}/api/playlists/${selectedPlaylist.value.id}/shuffle`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error('Failed to shuffle playlist')
    }

    const shuffledPlaylist = await response.json()
    
    // Emit shuffle complete event
    window.postMessage({
      type: 'SHUFFLE_COMPLETE',
      playlist: shuffledPlaylist
    }, '*')

  } catch (err) {
    console.error('Error shuffling playlist:', err)
    window.postMessage({
      type: 'SHUFFLE_ERROR',
      error: err instanceof Error ? err.message : 'Failed to shuffle playlist'
    }, '*')
  } finally {
    isShuffling.value = false
  }
}

onMounted(() => {
  if (authStore.isAuthenticated) {
    fetchPlaylists()
  }
})
</script>
